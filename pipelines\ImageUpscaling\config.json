{"name": "ImageUpscaling", "description": "Image upscaling using SwinIR Real-SR x4 only", "dependencies": {"python": ["torch>=2.0.0", "torchvision>=0.15.0", "pillow>=10.0.0", "numpy<2.0.0", "opencv-python>=4.8.0", "timm>=0.9.0", "einops>=0.6.0", "requests>=2.25.0", "tqdm>=4.64.0", "hf_transfer>=0.1.4"], "models": [{"name": "swinir-real-sr-x4", "repo_id": "valhalla/SwinIR-real-sr-L-x4-GAN", "required": true, "description": "SwinIR Real-World Super-Resolution x4 - High quality upscaler", "local_path": "ImageUpscaling/swinir-real-sr-x4", "files": ["003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth"]}]}}