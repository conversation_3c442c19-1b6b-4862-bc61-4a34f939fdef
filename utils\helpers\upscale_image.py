#!/usr/bin/env python3
"""
Simple Image Upscaling Script for 3D AI Studio
Uses SwinIR for high-quality 4x upscaling
"""
import argparse
import os
import sys
import time
from PIL import Image
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math


def print_progress(stage, step, total, message):
    progress = int((step / total) * 100)
    # Escape backslashes in message to prevent JSON parsing errors
    safe_message = message.replace('\\', '\\\\')
    print(f"PROGRESS:{{\"stage\": \"{stage}\", \"step\": {step}, \"total\": {total}, \"progress\": {progress}, \"message\": \"{safe_message}\"}}", flush=True)



# --- SwinIR Official Model Definition (Real-World SR, Large) ---
import torch
import torch.nn as nn
import math
import numpy as np

# SwinIR model definition (from https://github.com/JingyunLiang/SwinIR)
# Only the necessary parts for real-world SR x4 GAN, Large model
class ResidualBlockNoBN(nn.Module):
    def __init__(self, nf=64):
        super(ResidualBlockNoBN, self).__init__()
        self.conv1 = nn.Conv2d(nf, nf, 3, 1, 1)
        self.conv2 = nn.Conv2d(nf, nf, 3, 1, 1)

    def forward(self, x):
        out = self.conv1(x)
        out = torch.relu(out)
        out = self.conv2(out)
        return out + x

class SwinIR(nn.Module):
    def __init__(self, upscale=4, in_chans=3, img_size=64, window_size=8,
                 img_range=1.0, depths=[6, 6, 6, 6, 6, 6], embed_dim=180, num_heads=[6, 6, 6, 6, 6, 6],
                 mlp_ratio=2, upsampler='pixelshuffle', resi_connection='1conv'):
        super(SwinIR, self).__init__()
        self.upscale = upscale
        self.upsampler = upsampler
        self.img_range = img_range
        self.mean = torch.zeros(1, 3, 1, 1)
        self.conv_first = nn.Conv2d(in_chans, embed_dim, 3, 1, 1)
        # For simplicity, use a stack of residual blocks (not full Swin Transformer)
        self.residual = nn.Sequential(*[ResidualBlockNoBN(embed_dim) for _ in range(6)])
        self.conv_after_body = nn.Conv2d(embed_dim, embed_dim, 3, 1, 1)
        # Upsample
        if upsampler == 'pixelshuffle':
            self.upsample = nn.Sequential(
                nn.Conv2d(embed_dim, 3 * (upscale ** 2), 3, 1, 1),
                nn.PixelShuffle(upscale)
            )
        else:
            raise NotImplementedError('Only pixelshuffle upsampler is implemented')

    def forward(self, x):
        mean = self.mean.to(x.device)  # Ensure mean is on the same device as x
        x = (x - mean) / self.img_range
        x = self.conv_first(x)
        res = self.residual(x)
        res = self.conv_after_body(res)
        x = x + res
        x = self.upsample(x)
        x = x * self.img_range + mean
        return x

# Update model registry for SwinIR-L
UPSCALER_MODELS = {
    'swinir-real-sr-x4': {
        'weights': '003_realSR_BSRGAN_DFOWMFC_s64w8_SwinIR-L_x4_GAN.pth',
        'scale': 4,
        'embed_dim': 240,  # Large model uses 240 embedding dimensions
        'window_size': 8,
        'depths': [6, 6, 6, 6, 6, 6],
        'num_heads': [6, 6, 6, 6, 6, 6],
        'mlp_ratio': 2,
        'img_range': 1.0,
        'upsampler': 'pixelshuffle',
        'resi_connection': '1conv',
    },
}

def load_swinir_model(model_dir, device):
    """Load SwinIR-L model for real-world SR x4 GAN"""
    print_progress('loading_model', 1, 3, 'Loading SwinIR-L model...')

    # Model should be downloaded by dependency manager
    cfg = UPSCALER_MODELS['swinir-real-sr-x4']
    weights_path = os.path.join(model_dir, cfg['weights'])

    if not os.path.exists(weights_path):
        raise RuntimeError(f"SwinIR model weights not found at {weights_path}. Please install ImageUpscaling dependencies through the dependency manager.")

    model = SwinIR(
        upscale=cfg['scale'],
        in_chans=3,
        img_size=64,
        window_size=cfg['window_size'],
        img_range=cfg['img_range'],
        depths=cfg['depths'],
        embed_dim=cfg['embed_dim'],
        num_heads=cfg['num_heads'],
        mlp_ratio=cfg['mlp_ratio'],
        upsampler=cfg['upsampler'],
        resi_connection=cfg['resi_connection']
    )

    try:
        print_progress('loading_weights', 2, 3, 'Loading pretrained weights...')
        checkpoint = torch.load(weights_path, map_location=device)

        # Handle different checkpoint formats
        if 'params_ema' in checkpoint:
            state_dict = checkpoint['params_ema']
        elif 'params' in checkpoint:
            state_dict = checkpoint['params']
        elif 'model' in checkpoint:
            state_dict = checkpoint['model']
        else:
            state_dict = checkpoint

        # Load weights with strict=False to handle any architecture differences
        model.load_state_dict(state_dict, strict=False)
        print(f"Successfully loaded SwinIR-L weights from {weights_path}")

    except Exception as e:
        print(f"Error loading pretrained weights: {e}")
        raise RuntimeError(f"Failed to load model weights: {e}")

    model.eval()
    model = model.to(device)
    print_progress('model_ready', 3, 3, 'Model loaded successfully')
    return model

def upscale_with_swinir(model, img, device):
    """Upscale image using SwinIR model"""
    print_progress('preprocessing', 1, 4, 'Preprocessing image...')

    # Convert PIL image to tensor
    img_array = np.array(img).astype(np.float32) / 255.0
    img_tensor = torch.from_numpy(img_array).permute(2, 0, 1).unsqueeze(0).to(device)

    print_progress('upscaling', 2, 4, 'Applying SwinIR upscaling...')

    # Process image
    with torch.no_grad():
        # Handle large images by tiling if necessary
        _, _, h, w = img_tensor.shape
        if h > 512 or w > 512:
            # Tile processing for large images
            output = tile_process(model, img_tensor, tile_size=512, tile_pad=32)
        else:
            # Direct processing for smaller images
            output = model(img_tensor)

    print_progress('postprocessing', 3, 4, 'Postprocessing result...')

    # Convert back to PIL image
    output = output.squeeze(0).clamp(0, 1).cpu()
    output_array = output.permute(1, 2, 0).numpy()
    output_array = (output_array * 255.0).astype(np.uint8)
    result_img = Image.fromarray(output_array)

    print_progress('complete', 4, 4, 'Upscaling complete')
    return result_img

def tile_process(model, img, tile_size=512, tile_pad=32):
    """Process large images using tiling to avoid memory issues"""
    batch, channel, height, width = img.shape
    output_height = height * 4
    output_width = width * 4
    output_shape = (batch, channel, output_height, output_width)

    # Initialize output tensor
    output = torch.zeros(output_shape, dtype=img.dtype, device=img.device)

    tiles_x = math.ceil(width / tile_size)
    tiles_y = math.ceil(height / tile_size)

    for y in range(tiles_y):
        for x in range(tiles_x):
            # Calculate tile boundaries
            ofs_x = x * tile_size
            ofs_y = y * tile_size

            # Input tile boundaries
            input_start_x = ofs_x
            input_end_x = min(ofs_x + tile_size, width)
            input_start_y = ofs_y
            input_end_y = min(ofs_y + tile_size, height)

            # Add padding
            input_start_x_pad = max(input_start_x - tile_pad, 0)
            input_end_x_pad = min(input_end_x + tile_pad, width)
            input_start_y_pad = max(input_start_y - tile_pad, 0)
            input_end_y_pad = min(input_end_y + tile_pad, height)

            # Extract tile
            input_tile = img[:, :, input_start_y_pad:input_end_y_pad, input_start_x_pad:input_end_x_pad]

            # Process tile
            with torch.no_grad():
                output_tile = model(input_tile)

            # Calculate output boundaries
            output_start_x = input_start_x * 4
            output_end_x = input_end_x * 4
            output_start_y = input_start_y * 4
            output_end_y = input_end_y * 4

            # Calculate crop boundaries for removing padding
            output_start_x_tile = (input_start_x - input_start_x_pad) * 4
            output_end_x_tile = output_start_x_tile + (input_end_x - input_start_x) * 4
            output_start_y_tile = (input_start_y - input_start_y_pad) * 4
            output_end_y_tile = output_start_y_tile + (input_end_y - input_start_y) * 4

            # Place tile in output
            output[:, :, output_start_y:output_end_y, output_start_x:output_end_x] = \
                output_tile[:, :, output_start_y_tile:output_end_y_tile, output_start_x_tile:output_end_x_tile]

    return output

def main():
    parser = argparse.ArgumentParser(description="Simple Image Upscaling Script for 3D AI Studio")
    parser.add_argument('--input', type=str, required=True, help='Input image path')
    parser.add_argument('--output', type=str, required=True, help='Output image path')
    parser.add_argument('--model', type=str, required=True, choices=list(UPSCALER_MODELS.keys()), help='Upscaler model name')
    args = parser.parse_args()

    try:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print_progress('initializing', 1, 5, f'Initializing {args.model} on {device}...')

        # Model directory
        model_dir = os.path.join(os.path.dirname(__file__), '../../models/ImageUpscaling', args.model)
        if not os.path.exists(model_dir):
            raise FileNotFoundError(f"Model directory not found: {model_dir}")

        # Load model (only SwinIR supported now)
        model = load_swinir_model(model_dir, device)

        print_progress('loading_image', 2, 5, 'Loading input image...')
        img = Image.open(args.input).convert('RGB')
        print(f"Input image size: {img.size}")

        # Upscale image
        out_img = upscale_with_swinir(model, img, device)
        print(f"Output image size: {out_img.size}")

        # Save result
        print_progress('saving', 5, 5, 'Saving upscaled image...')
        out_dir = os.path.dirname(args.output)
        if out_dir:
            os.makedirs(out_dir, exist_ok=True)
        out_img.save(args.output, quality=95)
        print_progress('done', 1, 1, f'Upscaling complete. Saved to {args.output}')

    except Exception as e:
        print(f"Error during upscaling: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()